"""Label Generator routes for Talaria Dashboard."""

import os
import time
import uuid
from typing import Dict

from flask import Blueprint, current_app, jsonify, render_template, request, send_file
from flask_wtf.csrf import generate_csrf

# Import Talaria's custom authentication system
from core.auth.auth import admin_required, login_required

# Import the core label generation logic
from core.services.label_generator_service import LabelGeneratorService

# Create blueprint
label_generator_bp = Blueprint(
    "label_generator", __name__, url_prefix="/label-generator"
)

# Store generated files in memory for quick access
# This will store {session_id: {"label": label_path, "packing": packing_path, "timestamp": time.time()}}
GENERATED_FILES: Dict[str, Dict] = {}


@label_generator_bp.route("/")
@login_required
@admin_required
def index():
    """Render the label generator form page."""
    return render_template("label_generator.html", csrf_token=generate_csrf())


@label_generator_bp.route("/test", methods=["GET"])
@login_required
@admin_required
def test_route():
    """Test route to verify the blueprint is working."""
    return jsonify({"success": True, "message": "Label generator route is working!"})


@label_generator_bp.route("/generate", methods=["POST"])
@login_required
@admin_required
def generate_label():
    """Generate a label based on form inputs."""
    try:
        current_app.logger.info("Label generation request received")
        current_app.logger.info(f"Request form data: {dict(request.form)}")
        # Get form data
        form_data = {
            "label_type": request.form.get("label_type"),
            "shipment_date": request.form.get("shipment_date"),
            "project_name": request.form.get("project_name"),
            "wafer_number": int(request.form.get("wafer_number", 0)),
            "wafer_list": request.form.get("wafer_list"),
            "po": request.form.get("po", ""),
            "device_id": request.form.get("device_id", ""),
            "xfab_lot_id": request.form.get("xfab_lot_id", ""),
            "comments": request.form.get("comments", ""),
            "printer_ip": request.form.get("printer_ip", ""),
            "copy_number": int(request.form.get("copy_number", 0)),
            "download_type": request.form.get("download_type", "success"),
        }

        # Validate inputs for Erfurt label
        if "2" in form_data["label_type"] and (
            not form_data["po"]
            or not form_data["device_id"]
            or not form_data["xfab_lot_id"]
        ):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "For Erfurt label, please fill the PO, XFAB lot and Project ID fields!",
                    }
                ),
                400,
            )

        # Initialize label generator service
        current_app.logger.info("Initializing LabelGeneratorService...")
        service = LabelGeneratorService()
        current_app.logger.info("Service initialized successfully")

        # Generate the label
        current_app.logger.info("Starting label generation...")
        result = service.generate_label(form_data)
        current_app.logger.info(
            f"Label generation result: {result.get('success', False)}"
        )

        if not result["success"]:
            return jsonify(result), 500

        # Generate a unique session ID for this label generation
        session_id = str(uuid.uuid4())

        # Store the file paths in memory with the session ID
        GENERATED_FILES[session_id] = {
            "label": result["label_path"],
            "packing": result["packing_path"],
            "timestamp": time.time(),
            "print_success": result["print_success"],
            "copies": form_data["copy_number"],
            "label_filename": os.path.basename(result["label_path"]),
            "packing_filename": os.path.basename(result["packing_path"]),
        }

        # Clean up old files (older than 30 minutes)
        _cleanup_old_files()

        return jsonify(
            {
                "success": True,
                "session_id": session_id,
                "label_filename": os.path.basename(result["label_path"]),
                "packing_filename": os.path.basename(result["packing_path"]),
                "print_success": result["print_success"],
                "copies": form_data["copy_number"],
                "message": "Label generated successfully!",
            }
        )

    except Exception as e:
        current_app.logger.error(f"Error generating label: {str(e)}")
        return (
            jsonify({"success": False, "message": f"An error occurred: {str(e)}"}),
            500,
        )


@label_generator_bp.route("/download/<file_type>")
@login_required
@admin_required
def download_file(file_type):
    """Download a generated file."""
    session_id = request.args.get("session_id")

    if not session_id or session_id not in GENERATED_FILES:
        return (
            jsonify(
                {
                    "success": False,
                    "message": "Session expired or invalid. Please generate a new label.",
                }
            ),
            404,
        )

    # Get file path based on type
    if file_type == "packing":
        file_path = GENERATED_FILES[session_id]["packing"]
        filename = GENERATED_FILES[session_id]["packing_filename"]
    else:  # Default to label
        file_path = GENERATED_FILES[session_id]["label"]
        filename = GENERATED_FILES[session_id]["label_filename"]

    # Check if file exists
    if not os.path.exists(file_path):
        return (
            jsonify(
                {
                    "success": False,
                    "message": "File not found. Please generate a new label.",
                }
            ),
            404,
        )

    # Return the file
    return send_file(
        file_path,
        as_attachment=True,
        download_name=filename,
        mimetype="application/pdf",
    )


@label_generator_bp.route("/success")
@login_required
@admin_required
def success():
    """Show success page with download links."""
    session_id = request.args.get("session_id")

    if not session_id or session_id not in GENERATED_FILES:
        return render_template(
            "label_generator.html",
            error="Session expired. Please generate a new label.",
        )

    file_data = GENERATED_FILES[session_id]

    return render_template(
        "label_generator_success.html", session_id=session_id, **file_data
    )


def _cleanup_old_files():
    """Clean up old files (older than 30 minutes)."""
    current_time = time.time()
    expired_sessions = []

    for sid, data in GENERATED_FILES.items():
        if current_time - data["timestamp"] > 1800:  # 30 minutes
            expired_sessions.append(sid)

    for sid in expired_sessions:
        # Try to remove files from disk
        try:
            file_data = GENERATED_FILES[sid]
            for file_path in [file_data.get("label"), file_data.get("packing")]:
                if file_path and os.path.exists(file_path):
                    os.remove(file_path)
        except Exception as e:
            current_app.logger.warning(
                f"Could not clean up files for session {sid}: {str(e)}"
            )

        # Remove from memory
        GENERATED_FILES.pop(sid, None)
