/**
 * UX Enhancements CSS
 * Styles for breadcrumb navigation, progress indicators, and keyboard shortcuts
 */

/* Breadcrumb Navigation */
.breadcrumb-nav {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    padding: 12px 16px;
    border: 1px solid rgba(229, 231, 235, 0.8);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.breadcrumb-nav ol {
    margin: 0;
    padding: 0;
    list-style: none;
}

.breadcrumb-nav a {
    text-decoration: none;
    transition: all 0.2s ease;
}

.breadcrumb-nav a:hover {
    transform: translateY(-1px);
}

.breadcrumb-nav .fas {
    font-size: 0.875rem;
}

/* Progress Indicator */
#progress-container {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.progress-bar-container {
    position: relative;
    overflow: hidden;
}

#progress-bar {
    position: relative;
}

#progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Step Indicators */
.step-indicator {
    transition: all 0.3s ease;
    font-size: 0.75rem;
}

.step-indicator.completed {
    transform: scale(1.1);
}

.step-text {
    font-size: 0.75rem;
    color: #6b7280;
    transition: color 0.3s ease;
}

/* Keyboard Shortcuts Modal */
#shortcuts-modal {
    animation: fadeIn 0.2s ease-out;
}

#shortcuts-modal .bg-white {
    animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Keyboard shortcut keys */
kbd {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 0.75rem;
    font-weight: 600;
    border: 1px solid #d1d5db;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    min-width: 1.5rem;
    text-align: center;
}

/* Quick Action Buttons Enhancement */
.btn-quick-action {
    position: relative;
    overflow: hidden;
    transition: all 0.2s ease;
}

.btn-quick-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left 0.5s ease;
}

.btn-quick-action:hover::before {
    left: 100%;
}

.btn-quick-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Focus indicators for accessibility */
.breadcrumb-nav a:focus,
.btn-quick-action:focus,
.step-indicator:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
    border-radius: 4px;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .breadcrumb-nav {
        padding: 8px 12px;
        font-size: 0.875rem;
    }
    
    .breadcrumb-nav .fas {
        font-size: 0.75rem;
    }
    
    #progress-steps {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .step-indicator {
        margin-right: 0.5rem;
    }
    
    #shortcuts-modal .grid-cols-2 {
        grid-template-columns: 1fr;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .breadcrumb-nav {
        background: rgba(31, 41, 55, 0.95);
        border-color: rgba(75, 85, 99, 0.8);
    }
    
    .step-text {
        color: #9ca3af;
    }
    
    kbd {
        background-color: #374151;
        border-color: #4b5563;
        color: #f9fafb;
    }
}

/* Loading states */
.loading-shimmer {
    background: linear-gradient(
        90deg,
        #f0f0f0 25%,
        #e0e0e0 50%,
        #f0f0f0 75%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

/* Smooth transitions for all interactive elements */
.breadcrumb-nav *,
#progress-container *,
.btn-quick-action * {
    transition: all 0.2s ease;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .breadcrumb-nav {
        border: 2px solid #000;
    }

    .step-indicator {
        border-width: 2px;
    }

    kbd {
        border: 2px solid #000;
    }
}

/* Offline Mode Styles */
.offline-status {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.offline-status.online {
    background-color: #10b981;
    color: white;
    animation: slideInRight 0.3s ease-out;
}

.offline-status.offline {
    background-color: #f59e0b;
    color: white;
    animation: slideInRight 0.3s ease-out, pulse 2s infinite;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Offline mode disabled elements */
.offline-disabled {
    opacity: 0.5;
    pointer-events: none;
    position: relative;
}

.offline-disabled::after {
    content: 'Offline';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    z-index: 10;
}

/* Offline mode banner */
.offline-banner {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    padding: 12px;
    text-align: center;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.offline-banner .close-btn {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

.offline-banner .close-btn:hover {
    opacity: 1;
}

/* Cached data indicator */
.cached-data-indicator {
    display: inline-flex;
    align-items: center;
    background: #fef3c7;
    color: #92400e;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.75rem;
    margin-left: 8px;
}

.cached-data-indicator i {
    margin-right: 4px;
}
